[server]
port=8080

[postgresql]
host=""
port=5432
username="bilangpage_owner"
password=""
database="bilangpage"

[jwt]
secret="O2eKWGj4LoNPcPjLC649qDeNiQpfiFtiyn57E6sczDE="
issuer="bilangpage-api"

[translation]

[translation.deepl]
api_key="f14381f1-6908-2f24-39d6-9999d2282d8d:fx"
base_url="https://api-free.deepl.com"

[translation.deepseek]
api_key="***********************************"
base_url="https://api.deepseek.com"
model="deepseek-chat"

[translation.openrouter]
api_key="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"
base_url="https://openrouter.ai/api/v1"
model="gpt-4o-mini"

[apple]
key_id=""
issuer_id=""
bundle_id="com.wujiuye.BiLangePagePro"
private_key=""
is_production=false
