# 如何获取Apple配置项

## App Store Connect API配置 
1. 登录App Store Connect: https://appstoreconnect.apple.com/
2. 生成API密钥:
   * 进入"用户和访问" → "密钥"
   * 点击"生成API密钥"
   * 选择角色：App Manager 
   * 下载.p8私钥文件 
   * 记录Key ID（10个字符）和Issuer ID（UUID格式）
3. 配置到config.toml:

## 配置Apple Server-to-Server Notifications
1. 在App Store Connect中配置:
   * 进入你的App → "App信息" → "App Store服务器通知"
   * 设置通知URL: https://your-domain.com/api/apple/webhook
   * 选择通知版本：V2
2. 支持的通知类型:
   * DID_RENEW: 自动续期成功
   * EXPIRED: 订阅过期
   * DID_FAIL_TO_RENEW: 续期失败
   * REFUND: 退款
