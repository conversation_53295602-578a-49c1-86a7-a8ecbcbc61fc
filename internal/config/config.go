package config

import (
	"io"
	"os"
	"strings"

	"github.com/BurntSushi/toml"
	"github.com/bilangpage/bilangpage-api/pkg/util"
)

var C = new(Config)

type Config struct {
	Server      ServerConfig      `toml:"server"`
	PostgreSQL  PostgreSQLConfig  `toml:"postgresql"`
	JWT         JWTConfig         `toml:"jwt"`
	Translation TranslationConfig `toml:"translation"`
	Apple       AppleConfig       `toml:"apple"`
}

type ServerConfig struct {
	Port int `toml:"port"`
}

type PostgreSQLConfig struct {
	Host     string `toml:"host"`
	Port     int    `toml:"port"`
	Username string `toml:"username"`
	Password string `toml:"password"`
	Database string `toml:"database"`
}

type JWTConfig struct {
	Secret string `toml:"secret"`
	Issuer string `toml:"issuer"`
}

type TranslationConfig struct {
	DeepL      DeepLConfig      `toml:"deepl"`
	DeepSeek   DeepSeekConfig   `toml:"deepseek"`
	OpenRouter OpenRouterConfig `toml:"openrouter"`
}

type DeepLConfig struct {
	APIKey  string `toml:"api_key"`
	BaseURL string `toml:"base_url"`
}

type DeepSeekConfig struct {
	APIKey  string `toml:"api_key"`
	BaseURL string `toml:"base_url"`
	Model   string `toml:"model"`
}

type OpenRouterConfig struct {
	APIKey  string `toml:"api_key"`
	BaseURL string `toml:"base_url"`
	Model   string `toml:"model"`
}

type AppleConfig struct {
	KeyID        string `toml:"key_id"`
	IssuerID     string `toml:"issuer_id"`
	BundleID     string `toml:"bundle_id"`
	PrivateKey   string `toml:"private_key"`
	IsProduction bool   `toml:"is_production"`
}

func init() {
	var err error
	dir, _ := os.Getwd()
	if strings.Contains(dir, "/internal/") {
		index := strings.Index(dir, "/internal/")
		dir = dir[:index]
	}
	c, err := os.Open(dir + "/config.toml")
	if err != nil {
		util.AbnormalExit(err)
	}
	data, err := io.ReadAll(c)
	if err != nil {
		util.AbnormalExit(err)
	}
	if _, err = toml.Decode(string(data), C); err != nil {
		util.AbnormalExit(err)
	}
}
