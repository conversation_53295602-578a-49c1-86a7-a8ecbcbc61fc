package service

import (
	"errors"
	"fmt"
	"time"

	"github.com/bilangpage/bilangpage-api/internal/config"

	"github.com/bilangpage/bilangpage-api/internal/database"
	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/model"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SubscriptionService 订阅服务
type SubscriptionService struct {
	cacheService      *CacheService
	appleVerifier     *AppleSubscriptionVerifier
	enableAppleVerify bool
}

// NewSubscriptionService 创建订阅服务
func NewSubscriptionService() *SubscriptionService {
	service := &SubscriptionService{
		cacheService:      GetGlobalCacheService(),
		enableAppleVerify: false,
	}

	// 初始化Apple验证器（如果配置了相关参数）
	appleConfig := &config.C.Apple
	if appleConfig.KeyID != "" && appleConfig.IssuerID != "" && appleConfig.PrivateKey != "" {
		if verifier, err := NewAppleSubscriptionVerifier(
			appleConfig.KeyID,
			appleConfig.IssuerID,
			appleConfig.BundleID,
			appleConfig.PrivateKey,
			appleConfig.IsProduction,
		); err != nil {
			logrus.WithError(err).Warn("Failed to initialize Apple subscription verifier")
		} else {
			service.appleVerifier = verifier
			service.enableAppleVerify = true
			logrus.Info("Apple subscription verification enabled")
		}
	}

	return service
}

// SyncSubscription 同步订阅状态
func (s *SubscriptionService) SyncSubscription(userID string, request *dto.SubscriptionSyncRequest) (*dto.SubscriptionSyncResponse, error) {
	// 验证请求参数
	if err := request.Validate(); err != nil {
		logrus.WithField("request", request).Error("invalid request")
		return nil, fmt.Errorf("invalid request: %v", err)
	}

	// Apple服务端验证
	if s.enableAppleVerify && s.appleVerifier != nil {
		logrus.WithFields(logrus.Fields{
			"transactionID":         request.TransactionID,
			"originalTransactionID": request.OriginalTransactionID,
		}).Info("Starting Apple server-side verification")

		verifiedTransaction, err := s.appleVerifier.ValidateSubscription(
			request.TransactionID,
			request.OriginalTransactionID,
		)
		if err != nil {
			logrus.WithError(err).Error("Apple subscription verification failed")
			return nil, fmt.Errorf("subscription verification failed: %v", err)
		}

		// 验证bundle ID
		expectedBundleID := config.C.Apple.BundleID
		if verifiedTransaction.BundleID != expectedBundleID {
			return nil, fmt.Errorf("bundle ID mismatch: expected %s, got %s", expectedBundleID, verifiedTransaction.BundleID)
		}

		// 使用Apple验证的数据覆盖客户端数据
		if verifiedTransaction.ExpiresDate > 0 {
			// 确保时间戳是毫秒格式
			expiresDate := verifiedTransaction.ExpiresDate
			if expiresDate < 1e12 { // 如果小于1e12，可能是秒格式，转换为毫秒
				expiresDate *= 1000
			}
			request.ExpirationDate = &expiresDate
		}
		if verifiedTransaction.ProductID != "" {
			request.ProductID = verifiedTransaction.ProductID
		}

		logrus.WithFields(logrus.Fields{
			"transactionID": request.TransactionID,
			"productID":     verifiedTransaction.ProductID,
			"expiresDate":   verifiedTransaction.ExpiresDate,
		}).Info("Apple subscription verification successful")
	}

	// 是否重复提交
	// 备注：首次订阅时transactionId和originalTransactionId相同，后续续期时transactionId变化，originalTransactionId不变，
	// 因此只要transactionId不同就认为新订阅就可以，旧记录已经过期，同一个用户多条记录没关系
	exist := &model.Subscription{}
	if err := database.DB.Where("transaction_id = ?", request.TransactionID).First(exist).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to handle sync transaction: %v", err)
	} else if err == nil {
		return nil, fmt.Errorf("duplicate consumption transaction")
	}

	// 保存订阅信息
	nowTimestamp := time.Now().UnixMilli()
	aiQuotaCnt := 0
	// 未提供非续期订阅
	expiresAt := int64(0)
	if request.ExpirationDate == nil {
		return nil, fmt.Errorf("invalid request")
	} else {
		expiresAt = *request.ExpirationDate
		aiQuotaCnt = config.GetAiQuotaByProductId(request.ProductID)
	}

	subscription := &model.Subscription{
		UserID:                userID,
		ProductID:             request.ProductID,
		TransactionID:         request.TransactionID,
		OriginalTransactionID: request.OriginalTransactionID,
		ExpiresAt:             expiresAt,
		IsValid:               expiresAt > time.Now().UnixMilli(),
		Platform:              request.Platform,
		CreatedAt:             nowTimestamp,
		UpdatedAt:             nowTimestamp,
	}

	if err := database.DB.Transaction(func(tx *gorm.DB) error {
		if subErr := tx.Create(subscription).Error; subErr != nil {
			return fmt.Errorf("failed to create subscription: %w", subErr)
		}
		quotaService := GetGlobalAsyncQuotaService()
		if subErr := quotaService.SetUserQuota(tx, userID, aiQuotaCnt, false, expiresAt); subErr != nil {
			return fmt.Errorf("failed to add AI quota: %w", subErr)
		}
		return nil
	}); err != nil {
		logrus.WithField("request", request).WithError(err).Error("failed to handle sync subscription")
		return nil, fmt.Errorf("failed to handle sync subscription: %v", err)
	}

	// 使订阅缓存失效
	_ = s.cacheService.InvalidateSubscriptionCache(userID)

	response := &dto.SubscriptionSyncResponse{
		Subscription: &dto.SubscriptionInfo{
			ProductID:     request.ProductID,
			ExpiresAt:     expiresAt,
			IsValid:       true,
			TransactionID: request.TransactionID,
		},
	}

	logrus.WithField("request", request).Info("subscription sync successful")
	return response, nil
}

// GetSubscriptionStatus 获取订阅状态（带缓存）
func (s *SubscriptionService) GetSubscriptionStatus(userID string) (*dto.SubscriptionStatusResponse, error) {
	// 先尝试从缓存获取
	if cachedSubscription, err := s.cacheService.GetSubscriptionFromCache(userID); err == nil {
		return cachedSubscription, nil
	}

	// 缓存未命中，从数据库查询
	var subscriptions []model.Subscription

	// 查询用户所有有效的订阅
	err := database.DB.Where("user_id = ? AND is_valid = ?", userID, true).
		Order("expires_at DESC").
		Find(&subscriptions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to query subscription: %v", err)
	}

	if len(subscriptions) == 0 {
		// 无订阅时返回默认值
		defaultResponse := &dto.SubscriptionStatusResponse{
			ProductID: "",
			ExpiresAt: 0,
			IsValid:   false,
		}
		// 缓存默认值
		_ = s.cacheService.SetSubscriptionToCache(userID, defaultResponse)
		return defaultResponse, nil
	}

	// 先过滤掉过期的订阅
	var activeSubscriptions []model.Subscription
	for i := range subscriptions {
		if subscriptions[i].IsActive() {
			activeSubscriptions = append(activeSubscriptions, subscriptions[i])
		} else {
			subscriptions[i].IsValid = false
			subscriptions[i].UpdatedAt = time.Now().UnixMilli()
			if err := database.DB.Save(&subscriptions[i]).Error; err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"user_id":         userID,
					"subscription_id": subscriptions[i].ID,
					"product_id":      subscriptions[i].ProductID,
				}).Error("Failed to update expired subscription status")
			}
		}
	}

	if len(activeSubscriptions) == 0 {
		// 所有订阅都已过期
		expiredResponse := &dto.SubscriptionStatusResponse{
			ProductID: "",
			ExpiresAt: 0,
			IsValid:   false,
		}
		// 缓存过期状态
		_ = s.cacheService.SetSubscriptionToCache(userID, expiredResponse)
		return expiredResponse, nil
	}

	// 按产品优先级排序订阅
	prioritizedSubscription := s.getHighestPrioritySubscription(activeSubscriptions)

	response := &dto.SubscriptionStatusResponse{
		ProductID: prioritizedSubscription.ProductID,
		ExpiresAt: prioritizedSubscription.ExpiresAt,
		IsValid:   true,
	}

	// 缓存结果
	_ = s.cacheService.SetSubscriptionToCache(userID, response)

	return response, nil
}

// getHighestPrioritySubscription 根据产品优先级获取最高优先级的订阅
func (s *SubscriptionService) getHighestPrioritySubscription(subscriptions []model.Subscription) *model.Subscription {
	if len(subscriptions) == 0 {
		return nil
	}

	// 定义产品优先级（数字越小优先级越高）
	priorityMap := map[string]int{
		config.MaxMonth: 1,
		config.ProMonth: 2,
	}

	var highestPrioritySubscription *model.Subscription
	highestPriority := 999

	for i := range subscriptions {
		subscription := &subscriptions[i]
		priority, exists := priorityMap[subscription.ProductID]
		if !exists {
			priority = 100
		}
		// 如果优先级更高，或者优先级相同但过期时间更晚
		if highestPrioritySubscription == nil ||
			priority < highestPriority ||
			(priority == highestPriority && subscription.ExpiresAt > highestPrioritySubscription.ExpiresAt) {
			highestPriority = priority
			highestPrioritySubscription = subscription
		}
	}

	return highestPrioritySubscription
}

// HandleSubscriptionRenewal 处理订阅续期（webhook调用）
func (s *SubscriptionService) HandleSubscriptionRenewal(userID string, transactionInfo *DecodedTransactionInfo) error {
	logrus.WithFields(logrus.Fields{
		"userID":        userID,
		"transactionID": transactionInfo.TransactionID,
		"productID":     transactionInfo.ProductID,
		"expiresDate":   transactionInfo.ExpiresDate,
	}).Info("Processing subscription renewal from webhook")

	// 验证bundle ID
	expectedBundleID := config.C.Apple.BundleID
	if transactionInfo.BundleID != expectedBundleID {
		return fmt.Errorf("bundle ID mismatch: expected %s, got %s", expectedBundleID, transactionInfo.BundleID)
	}

	// 确保时间戳是毫秒格式
	expiresDate := transactionInfo.ExpiresDate
	if expiresDate < 1e12 { // 如果小于1e12，可能是秒格式，转换为毫秒
		expiresDate *= 1000
	}

	now := time.Now().UnixMilli()

	// 查找现有订阅并验证用户ID
	subscription := &model.Subscription{}
	err := database.DB.Where("original_transaction_id = ? AND user_id = ?", transactionInfo.OriginalTransactionID, userID).First(subscription).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("subscription not found for user %s with original_transaction_id %s", userID, transactionInfo.OriginalTransactionID)
		}
		return fmt.Errorf("failed to query subscription: %w", err)
	}

	// 更新现有订阅
	subscription.TransactionID = transactionInfo.TransactionID // 更新为最新的transaction_id
	subscription.ExpiresAt = expiresDate                       // 使用处理过的时间戳
	subscription.IsValid = expiresDate > now
	subscription.UpdatedAt = now

	return database.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Save(&subscription).Error; err != nil {
			return fmt.Errorf("failed to update subscription: %w", err)
		}

		// 处理续期额度
		if subscription.IsValid {
			aiQuotaCnt := config.GetAiQuotaByProductId(transactionInfo.ProductID)
			quotaService := GetGlobalAsyncQuotaService()
			if err := quotaService.SetUserQuota(tx, userID, aiQuotaCnt, true, expiresDate); err != nil {
				return fmt.Errorf("failed to update user quota: %w", err)
			}
		}

		// 使订阅缓存失效
		_ = s.cacheService.InvalidateSubscriptionCache(userID)
		return nil
	})
}

// HandleSubscriptionRefund 处理订阅退款（webhook调用）
func (s *SubscriptionService) HandleSubscriptionRefund(userID, transactionID string) error {
	logrus.WithFields(logrus.Fields{
		"userID":        userID,
		"transactionID": transactionID,
	}).Info("Processing subscription refund from webhook")

	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 撤销订阅
		if err := tx.Model(&model.Subscription{}).
			Where("user_id = ? AND transaction_id = ?", userID, transactionID).
			Update("is_valid", false).Error; err != nil {
			return fmt.Errorf("failed to revoke subscription: %w", err)
		}

		// 重置用户额度并添加变更记录
		quotaService := GetGlobalAsyncQuotaService()
		if err := quotaService.ResetUserQuotaForRefund(tx, userID, transactionID); err != nil {
			return fmt.Errorf("failed to reset user quota: %w", err)
		}

		// 使订阅缓存失效
		_ = s.cacheService.InvalidateSubscriptionCache(userID)
		return nil
	})
}
