package service

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// AppleSubscriptionVerifier Apple订阅验证服务
type AppleSubscriptionVerifier struct {
	keyID      string
	issuerID   string
	bundleID   string
	privateKey *ecdsa.PrivateKey
	baseURL    string // 生产环境: https://api.storekit.itunes.apple.com, 沙盒: https://api.storekit-sandbox.itunes.apple.com
}

// AppleServerAPIJWTClaims Apple Server API JWT Claims (不同于OAuth的Claims)
type AppleServerAPIJWTClaims struct {
	Iss string `json:"iss"`
	Iat int64  `json:"iat"`
	Exp int64  `json:"exp"`
	Aud string `json:"aud"`
	Bid string `json:"bid"`
	jwt.RegisteredClaims
}

// TransactionInfoResponse App Store Server API响应
type TransactionInfoResponse struct {
	SignedTransactionInfo string `json:"signedTransactionInfo"`
}

// DecodedTransactionInfo 解码后的交易信息
type DecodedTransactionInfo struct {
	TransactionID         string `json:"transactionId"`
	OriginalTransactionID string `json:"originalTransactionId"`
	ProductID             string `json:"productId"`
	BundleID              string `json:"bundleId"`
	ExpiresDate           int64  `json:"expiresDate"`
	PurchaseDate          int64  `json:"purchaseDate"`
	Type                  string `json:"type"`
	InAppOwnershipType    string `json:"inAppOwnershipType"`
	SignedDate            int64  `json:"signedDate"`
}

// SubscriptionStatusResponse 订阅状态响应
type SubscriptionStatusResponse struct {
	Environment string                    `json:"environment"`
	BundleID    string                    `json:"bundleId"`
	AppAppleID  int64                     `json:"appAppleId"`
	Data        []SubscriptionGroupStatus `json:"data"`
}

// SubscriptionGroupStatus 订阅组状态
type SubscriptionGroupStatus struct {
	SubscriptionGroupIdentifier string                `json:"subscriptionGroupIdentifier"`
	LastTransactions            []LastTransactionItem `json:"lastTransactions"`
}

// LastTransactionItem 最后交易项
type LastTransactionItem struct {
	OriginalTransactionID string `json:"originalTransactionId"`
	Status                int    `json:"status"` // 1: Active, 2: Expired, 3: In Billing Retry Period, 4: In Grace Period, 5: Revoked
	SignedTransactionInfo string `json:"signedTransactionInfo"`
	SignedRenewalInfo     string `json:"signedRenewalInfo"`
}

// NewAppleSubscriptionVerifier 创建Apple订阅验证器
func NewAppleSubscriptionVerifier(keyID, issuerID, bundleID, privateKeyPEM string, isProduction bool) (*AppleSubscriptionVerifier, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	ecdsaKey, ok := privateKey.(*ecdsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("private key is not ECDSA")
	}

	baseURL := "https://api.storekit-sandbox.itunes.apple.com"
	if isProduction {
		baseURL = "https://api.storekit.itunes.apple.com"
	}

	return &AppleSubscriptionVerifier{
		keyID:      keyID,
		issuerID:   issuerID,
		bundleID:   bundleID,
		privateKey: ecdsaKey,
		baseURL:    baseURL,
	}, nil
}

// generateJWT 生成用于App Store Server API的JWT
func (v *AppleSubscriptionVerifier) generateJWT() (string, error) {
	now := time.Now()
	claims := AppleServerAPIJWTClaims{
		Iss: v.issuerID,
		Iat: now.Unix(),
		Exp: now.Add(time.Hour).Unix(),
		Aud: "appstoreconnect-v1",
		Bid: v.bundleID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	token.Header["kid"] = v.keyID

	return token.SignedString(v.privateKey)
}

// VerifyTransaction 验证单个交易
func (v *AppleSubscriptionVerifier) VerifyTransaction(transactionID string) (*DecodedTransactionInfo, error) {
	jwtToken, err := v.generateJWT()
	if err != nil {
		return nil, fmt.Errorf("failed to generate JWT: %w", err)
	}

	url := fmt.Sprintf("%s/inApps/v1/transactions/%s", v.baseURL, transactionID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+jwtToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response TransactionInfoResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// 解码签名的交易信息
	return v.decodeSignedTransactionInfo(response.SignedTransactionInfo)
}

// GetSubscriptionStatus 获取订阅状态
func (v *AppleSubscriptionVerifier) GetSubscriptionStatus(originalTransactionID string) (*SubscriptionStatusResponse, error) {
	jwtToken, err := v.generateJWT()
	if err != nil {
		return nil, fmt.Errorf("failed to generate JWT: %w", err)
	}

	url := fmt.Sprintf("%s/inApps/v1/subscriptions/%s", v.baseURL, originalTransactionID)
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+jwtToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response SubscriptionStatusResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &response, nil
}

// decodeSignedTransactionInfo 解码签名的交易信息
func (v *AppleSubscriptionVerifier) decodeSignedTransactionInfo(signedInfo string) (*DecodedTransactionInfo, error) {
	// 解析JWT payload
	parts := bytes.Split([]byte(signedInfo), []byte("."))
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid JWT format")
	}

	// Base64解码payload
	payload, err := v.base64Decode(string(parts[1]))
	if err != nil {
		return nil, fmt.Errorf("failed to decode payload: %w", err)
	}

	var transactionInfo DecodedTransactionInfo
	if err := json.Unmarshal(payload, &transactionInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal transaction info: %w", err)
	}

	return &transactionInfo, nil
}

// DecodeSignedTransactionInfo 公开的解码方法，供webhook使用
func (v *AppleSubscriptionVerifier) DecodeSignedTransactionInfo(signedInfo string) (*DecodedTransactionInfo, error) {
	return v.decodeSignedTransactionInfo(signedInfo)
}

// VerifyWebhookSignature 验证Apple webhook签名
func (v *AppleSubscriptionVerifier) VerifyWebhookSignature(signedPayload string) error {
	if signedPayload == "" {
		return fmt.Errorf("signed payload is empty")
	}

	// 解析JWT并验证签名
	token, err := jwt.Parse(signedPayload, func(token *jwt.Token) (interface{}, error) {
		return v.getAppleWebhookPublicKey(token)
	})

	if err != nil {
		return fmt.Errorf("failed to parse signed payload: %w", err)
	}

	// 验证claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 验证bundle ID
		if bundleID, exists := claims["bundleId"]; exists {
			if bundleID != v.bundleID {
				return fmt.Errorf("bundle ID mismatch: expected %s, got %s", v.bundleID, bundleID)
			}
		} else {
			return fmt.Errorf("bundle ID not found in payload")
		}

		// 验证环境
		if environment, exists := claims["environment"]; exists {
			expectedEnv := "Sandbox"
			if v.baseURL == "https://api.storekit.itunes.apple.com" {
				expectedEnv = "Production"
			}
			if environment != expectedEnv {
				return fmt.Errorf("environment mismatch: expected %s, got %s", expectedEnv, environment)
			}
		}
	} else {
		return fmt.Errorf("invalid token claims")
	}

	return nil
}

// getAppleWebhookPublicKey 获取Apple webhook的公钥
func (v *AppleSubscriptionVerifier) getAppleWebhookPublicKey(token *jwt.Token) (interface{}, error) {
	// 检查签名算法
	if token.Method.Alg() != "ES256" {
		return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
	}

	// 从Apple获取证书链
	resp, err := http.Get("https://www.apple.com/certificateauthority/AppleRootCA-G3.cer")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple certificate: %w", err)
	}
	defer resp.Body.Close()

	certData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read certificate: %w", err)
	}

	// 解析证书
	cert, err := x509.ParseCertificate(certData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %w", err)
	}

	return cert.PublicKey, nil
}

// base64Decode 解码base64字符串（处理URL安全的base64）
func (v *AppleSubscriptionVerifier) base64Decode(s string) ([]byte, error) {
	// 添加必要的填充
	switch len(s) % 4 {
	case 2:
		s += "=="
	case 3:
		s += "="
	}

	// 替换URL安全字符
	s = string(bytes.ReplaceAll([]byte(s), []byte("-"), []byte("+")))
	s = string(bytes.ReplaceAll([]byte(s), []byte("_"), []byte("/")))

	return base64.StdEncoding.DecodeString(s)
}

// ValidateSubscription 验证订阅的完整流程
func (v *AppleSubscriptionVerifier) ValidateSubscription(transactionID, originalTransactionID string) (*DecodedTransactionInfo, error) {
	// 首先验证单个交易
	transactionInfo, err := v.VerifyTransaction(transactionID)
	if err != nil {
		return nil, fmt.Errorf("failed to verify transaction: %w", err)
	}

	// 验证交易信息的一致性
	if transactionInfo.TransactionID != transactionID {
		return nil, fmt.Errorf("transaction ID mismatch")
	}

	if transactionInfo.OriginalTransactionID != originalTransactionID {
		return nil, fmt.Errorf("original transaction ID mismatch")
	}

	if transactionInfo.BundleID != v.bundleID {
		return nil, fmt.Errorf("bundle ID mismatch")
	}

	// 检查订阅是否仍然有效
	if transactionInfo.ExpiresDate > 0 && transactionInfo.ExpiresDate < time.Now().UnixMilli() {
		return nil, fmt.Errorf("subscription has expired")
	}

	return transactionInfo, nil
}
