package filter

import (
	"strings"

	"github.com/bilangpage/bilangpage-api/internal/dto"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/gin-gonic/gin"
)

var jwtService = service.NewJWTService()

func TokenAuthFilter() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过心跳检测路由的鉴权
		if c.Request.URL.Path == "/status" {
			c.Next()
			return
		}

		// 跳过Apple登录接口和Webhook路由的鉴权
		if strings.HasPrefix(c.Request.URL.Path, "/api/auth/apple") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/apple/webhook") {
			c.Next()
			return
		}

		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			respondUnauthorized(c, "Missing authorization header")
			return
		}

		// 提取token
		token := extractTokenFromHeader(authHeader)
		if token == "" {
			respondUnauthorized(c, "Invalid authorization header format")
			return
		}

		// 验证token
		claims, err := jwtService.ValidateToken(token)
		if err != nil {
			respondUnauthorized(c, "Invalid or expired token")
			return
		}

		// 将用户信息存储到context中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("provider", claims.Provider)

		// 继续处理请求
		c.Next()
	}
}

// extractTokenFromHeader 从请求头中提取token
func extractTokenFromHeader(authHeader string) string {
	if authHeader == "" {
		return ""
	}

	parts := strings.SplitN(authHeader, " ", 2)
	if len(parts) != 2 || parts[0] != "Bearer" {
		return ""
	}

	return parts[1]
}

// respondUnauthorized 返回401错误响应
func respondUnauthorized(c *gin.Context, message string) {
	response := dto.NewErrorResponse(401, message)
	c.JSON(401, response)
	c.Abort()
}
