package restapi

import (
	"errors"
	"fmt"
	"github.com/bilangpage/bilangpage-api/internal/config"
	"github.com/bilangpage/bilangpage-api/internal/database"
	"github.com/bilangpage/bilangpage-api/internal/model"
	"github.com/bilangpage/bilangpage-api/internal/service"
	"github.com/bilangpage/bilangpage-api/internal/web"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"net/http"
)

func init() {
	appleWebhookAPI := &appleWebhookRestAPI{}

	web.SetOptions(func(engine *gin.Engine) {
		engine.POST("/api/apple/webhook", appleWebhookAPI.handleAppleNotification)
	})
}

type appleWebhookRestAPI struct{}

// AppleNotificationPayload Apple通知载荷
type AppleNotificationPayload struct {
	NotificationType string                `json:"notificationType"`
	Subtype          string                `json:"subtype,omitempty"`
	NotificationUUID string                `json:"notificationUUID"`
	Data             AppleNotificationData `json:"data"`
	Version          string                `json:"version"`
	SignedDate       int64                 `json:"signedDate"`
}

// AppleNotificationData 通知数据
type AppleNotificationData struct {
	AppAppleID            int64  `json:"appAppleId"`
	BundleID              string `json:"bundleId"`
	BundleVersion         string `json:"bundleVersion"`
	Environment           string `json:"environment"`
	SignedRenewalInfo     string `json:"signedRenewalInfo"`
	SignedTransactionInfo string `json:"signedTransactionInfo"`
}

// handleAppleNotification 处理Apple通知
func (a *appleWebhookRestAPI) handleAppleNotification(c *gin.Context) {
	// 验证请求签名
	signedPayload := c.GetHeader("x-apple-signed-payload")
	if signedPayload == "" {
		logrus.Error("Missing x-apple-signed-payload header")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing signature"})
		return
	}

	// 创建验证器并验证签名
	verifier, err := a.getAppleVerifier()
	if err != nil {
		logrus.WithError(err).Error("Failed to create Apple verifier")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Configuration error"})
		return
	}

	if err := verifier.VerifyWebhookSignature(signedPayload); err != nil {
		logrus.WithError(err).Error("Apple notification signature verification failed")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
		return
	}

	var payload AppleNotificationPayload
	if err := c.ShouldBindJSON(&payload); err != nil {
		logrus.WithError(err).Error("Failed to parse Apple notification")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload"})
		return
	}

	logrus.WithFields(logrus.Fields{
		"notificationType": payload.NotificationType,
		"subtype":          payload.Subtype,
		"bundleId":         payload.Data.BundleID,
		"environment":      payload.Data.Environment,
	}).Info("Received Apple notification")

	// 处理不同类型的通知
	subscriptionService := service.NewSubscriptionService()

	switch payload.NotificationType {
	case "DID_RENEW":
		err := a.handleSubscriptionRenewal(payload, subscriptionService)
		if err != nil {
			logrus.WithError(err).Error("Failed to handle subscription renewal")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Processing failed"})
			return
		}
	case "REFUND":
		err := a.handleRefund(payload, subscriptionService)
		if err != nil {
			logrus.WithError(err).Error("Failed to handle refund")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Processing failed"})
			return
		}
	default:
		logrus.WithField("notificationType", payload.NotificationType).Info("Unhandled notification type")
	}

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}

// handleSubscriptionRenewal 处理订阅续期
func (a *appleWebhookRestAPI) handleSubscriptionRenewal(payload AppleNotificationPayload, subscriptionService *service.SubscriptionService) error {
	// 解析交易信息
	transactionInfo, err := a.parseTransactionInfo(payload.Data.SignedTransactionInfo)
	if err != nil {
		return fmt.Errorf("failed to parse transaction info: %w", err)
	}

	// 查找用户
	userID, err := a.findUserByOriginalTransactionID(transactionInfo.OriginalTransactionID)
	if err != nil {
		return fmt.Errorf("failed to find user: %w", err)
	}

	// 调用订阅服务处理续期
	return subscriptionService.HandleSubscriptionRenewal(userID, transactionInfo)
}

// handleRefund 处理退款
func (a *appleWebhookRestAPI) handleRefund(payload AppleNotificationPayload, subscriptionService *service.SubscriptionService) error {
	transactionInfo, err := a.parseTransactionInfo(payload.Data.SignedTransactionInfo)
	if err != nil {
		return fmt.Errorf("failed to parse transaction info: %w", err)
	}

	userID, err := a.findUserByOriginalTransactionID(transactionInfo.OriginalTransactionID)
	if err != nil {
		return fmt.Errorf("failed to find user: %w", err)
	}

	// 调用订阅服务处理退款
	return subscriptionService.HandleSubscriptionRefund(userID, transactionInfo.TransactionID)
}

// parseTransactionInfo 解析交易信息
func (a *appleWebhookRestAPI) parseTransactionInfo(signedTransactionInfo string) (*service.DecodedTransactionInfo, error) {
	verifier, err := a.getAppleVerifier()
	if err != nil {
		return nil, fmt.Errorf("failed to create verifier: %w", err)
	}
	return verifier.DecodeSignedTransactionInfo(signedTransactionInfo)
}

// findUserByOriginalTransactionID 根据原始交易ID查找用户
func (a *appleWebhookRestAPI) findUserByOriginalTransactionID(originalTransactionID string) (string, error) {
	var subscription model.Subscription
	err := database.DB.Where("original_transaction_id = ?", originalTransactionID).First(&subscription).Error
	if err != nil {
		return "", err
	}
	return subscription.UserID, nil
}

// getAppleVerifier 获取Apple验证器
func (a *appleWebhookRestAPI) getAppleVerifier() (*service.AppleSubscriptionVerifier, error) {
	appleConfig := &config.C.Apple
	if appleConfig.KeyID == "" || appleConfig.IssuerID == "" || appleConfig.PrivateKey == "" {
		return nil, errors.New("apple configuration is incomplete")
	}

	return service.NewAppleSubscriptionVerifier(
		appleConfig.KeyID,
		appleConfig.IssuerID,
		appleConfig.BundleID,
		appleConfig.PrivateKey,
		appleConfig.IsProduction,
	)
}
